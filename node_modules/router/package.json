{"name": "router", "description": "Simple middleware-style router", "version": "1.3.8", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "repository": "pillarjs/router", "dependencies": {"array-flatten": "3.0.0", "debug": "2.6.9", "methods": "~1.1.2", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "setprototypeof": "1.2.0", "utils-merge": "1.0.1"}, "devDependencies": {"after": "0.8.2", "eslint": "8.34.0", "eslint-plugin-markdown": "3.0.0", "finalhandler": "1.2.0", "mocha": "10.2.0", "nyc": "15.1.0", "safe-buffer": "5.2.1", "supertest": "6.3.3"}, "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "SECURITY.md", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}}