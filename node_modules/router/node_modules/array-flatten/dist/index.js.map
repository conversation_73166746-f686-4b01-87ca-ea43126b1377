{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;AAcA;;GAEG;AACH,SAAgB,OAAO,CAA2B,KAAQ;IACxD,IAAM,MAAM,GAAiB,EAAE,CAAC;IAChC,QAAQ,CAAI,KAAK,EAAE,MAAM,CAAC,CAAC;IAC3B,OAAO,MAAM,CAAC;AAChB,CAAC;AAJD,0BAIC;AAED;;GAEG;AACH,SAAS,QAAQ,CACf,KAAQ,EACR,MAAoB;IAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,IAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEvB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,QAAQ,CAAC,KAAY,EAAE,MAAM,CAAC,CAAC;SAChC;aAAM;YACL,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACpB;KACF;AACH,CAAC", "sourcesContent": ["/**\n * Pick the value from an array.\n */\nexport type PickValue<T> = T extends ReadonlyArray<any>\n  ? {\n      [K in Extract<keyof T, number>]: PickValue<T[K]>;\n    }[number]\n  : T;\n\n/**\n * Flatten an `ArrayLike` object in TypeScript.\n */\nexport type FlatArray<T extends ArrayLike<any>> = Array<PickValue<T[number]>>;\n\n/**\n * Flatten an array indefinitely.\n */\nexport function flatten<T extends ArrayLike<any>>(array: T): FlatArray<T> {\n  const result: FlatArray<T> = [];\n  $flatten<T>(array, result);\n  return result;\n}\n\n/**\n * Internal flatten function recursively passes `result`.\n */\nfunction $flatten<T extends ArrayLike<any>>(\n  array: T,\n  result: FlatArray<T>\n): void {\n  for (let i = 0; i < array.length; i++) {\n    const value = array[i];\n\n    if (Array.isArray(value)) {\n      $flatten(value as any, result);\n    } else {\n      result.push(value);\n    }\n  }\n}\n"]}