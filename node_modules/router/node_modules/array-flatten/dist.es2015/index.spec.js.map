{"version": 3, "file": "index.spec.js", "sourceRoot": "", "sources": ["../src/index.spec.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AACvC,OAAO,EAAE,OAAO,EAAE,MAAM,SAAS,CAAC;AAElC,QAAQ,CAAC,SAAS,EAAE;IAClB,EAAE,CAAC,yBAAyB,EAAE;QAC5B,IAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAEpE,UAAU,CAAW,MAAM,CAAC,CAAC;QAE7B,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAChE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6BAA6B,EAAE;QAChC,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAE/B,UAAU,CAAW,MAAM,CAAC,CAAC;QAE7B,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iCAAiC,EAAE;QACpC,IAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAU,CAAC;QAC1C,IAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;QAE9B,UAAU,CAAoB,MAAM,CAAC,CAAC;QAEtC,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4BAA4B,EAAE;QAC/B,IAAM,KAAK,GAAG,CAAC;YACb,OAAO,SAAS,CAAC;QACnB,CAAC,CAAC,EAAE,CAAC;QACL,IAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;QAE9B,UAAU,CAAQ,MAAM,CAAC,CAAC;QAE1B,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,8BAA8B,EAAE;QACjC,IAAM,EAAE,GAAG,UAAC,CAAS,IAAK,OAAA,CAAC,EAAD,CAAC,CAAC;QAC5B,IAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,IAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;QAE9B,UAAU,CAA4C,MAAM,CAAC,CAAC;QAE9D,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yBAAyB,EAAE;QAC5B,IAAM,KAAK,GAAyC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,IAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;QAE9B,UAAU,CAAW,MAAM,CAAC,CAAC;QAE7B,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { expectType } from \"ts-expect\";\nimport { flatten } from \"./index\";\n\ndescribe(\"flatten\", () => {\n  it(\"should flatten an array\", () => {\n    const result = flatten([1, [2, [3, [4, [5]]], 6, [[7], 8], 9], 10]);\n\n    expectType<number[]>(result);\n\n    expect(result).toStrictEqual([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);\n  });\n\n  it(\"should work with array-like\", () => {\n    const result = flatten(\"test\");\n\n    expectType<string[]>(result);\n\n    expect(result).toStrictEqual([\"t\", \"e\", \"s\", \"t\"]);\n  });\n\n  it(\"should work with readonly array\", () => {\n    const input = [1, [2, [3, [4]]]] as const;\n    const result = flatten(input);\n\n    expectType<(1 | 2 | 3 | 4)[]>(result);\n\n    expect(result).toStrictEqual([1, 2, 3, 4]);\n  });\n\n  it(\"should work with arguments\", () => {\n    const input = (function() {\n      return arguments;\n    })();\n    const result = flatten(input);\n\n    expectType<any[]>(result);\n\n    expect(result).toStrictEqual([]);\n  });\n\n  it(\"should work with mixed types\", () => {\n    const fn = (x: string) => x;\n    const input = [1, [\"test\", [fn, [true]]]];\n    const result = flatten(input);\n\n    expectType<(number | string | boolean | typeof fn)[]>(result);\n\n    expect(result).toStrictEqual([1, \"test\", fn, true]);\n  });\n\n  it(\"should work with tuples\", () => {\n    const input: [number, [number, number], [number]] = [1, [1, 2], [3]];\n    const result = flatten(input);\n\n    expectType<number[]>(result);\n\n    expect(result).toStrictEqual([1, 1, 2, 3]);\n  });\n});\n"]}