#!/usr/bin/env python3

import argparse
import json
import sys
import requests
from bs4 import BeautifulSoup
import re
import hashlib
import pickle
import time
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Cache per TMDB e vixsrc
CACHE_FILE_TMDB = '/tmp/tmdb_vixsrc_cache.pkl'
cache_data = {}

def load_cache_tmdb():
    global cache_data
    try:
        with open(CACHE_FILE_TMDB, 'rb') as f:
            cache_data = pickle.load(f)
    except (FileNotFoundError, EOFError):
        cache_data = {}

def save_cache_tmdb():
    try:
        # Limita dimensione cache (mantieni solo gli ultimi 1000 elementi)
        if len(cache_data) > 1000:
            # Mantieni solo i più recenti (approssimazione)
            items = list(cache_data.items())
            cache_data.clear()
            cache_data.update(items[-500:])  # Mantieni gli ultimi 500

        with open(CACHE_FILE_TMDB, 'wb') as f:
            pickle.dump(cache_data, f)
    except Exception:
        pass

def get_cache_key_tmdb(tmdb_id, is_tv):
    return f"tmdb_{tmdb_id}_{is_tv}"

def get_cache_key_search(title, year, is_tv):
    return hashlib.md5(f"search_{title}_{year}_{is_tv}".encode()).hexdigest()

# Sessione HTTP ottimizzata con retry e connection pooling
def create_session():
    session = requests.Session()

    # Strategia di retry
    retry_strategy = Retry(
        total=3,
        backoff_factor=0.5,
        status_forcelist=[429, 500, 502, 503, 504],
    )

    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=10,
        pool_maxsize=20
    )

    session.mount("http://", adapter)
    session.mount("https://", adapter)

    # Headers ottimizzati
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
    })

    return session

# Sessione globale
http_session = create_session()

# Function to search vixsrc.to for a movie or TV show by title and year
# Modifico la funzione search_vixsrc per utilizzare l'ID TMDB direttamente
def search_vixsrc(title, year=None, is_tv=False, tmdb_id=None):
    try:
        # Carica cache
        load_cache_tmdb()

        # Controlla cache per accesso diretto
        if tmdb_id:
            cache_key = get_cache_key_tmdb(tmdb_id, is_tv)
            if cache_key in cache_data:
                print("Using cached TMDB result", file=sys.stderr)
                return cache_data[cache_key]

        # Controlla cache per ricerca
        search_cache_key = get_cache_key_search(title, year, is_tv)
        if search_cache_key in cache_data:
            print("Using cached search result", file=sys.stderr)
            return cache_data[search_cache_key]

        # Se abbiamo un TMDB ID, proviamo prima ad accedere direttamente all'URL con quell'ID
        if tmdb_id:
            # Determina l'URL diretto basato sul tipo di contenuto
            if is_tv:
                direct_url = f"https://vixsrc.to/tv/{tmdb_id}/1/1/"
            else:
                direct_url = f"https://vixsrc.to/movie/{tmdb_id}/"

            # Invia la richiesta con sessione ottimizzata
            response = http_session.get(direct_url, timeout=10)

            # Se la risposta è 200 OK, abbiamo trovato la pagina
            if response.status_code == 200:
                print(f"Found direct match using TMDB ID at {direct_url}", file=sys.stderr)
                # Salva in cache
                cache_data[get_cache_key_tmdb(tmdb_id, is_tv)] = tmdb_id
                save_cache_tmdb()
                return tmdb_id
            else:
                print(f"Direct access with TMDB ID failed (status code: {response.status_code}), falling back to search", file=sys.stderr)

        # Se l'accesso diretto fallisce o non abbiamo un TMDB ID, procediamo con la ricerca per titolo
        # Format the search query
        query = title
        if year:
            query = f"{title} {year}"

        # URL encode the query
        query = requests.utils.quote(query)

        # Determine the search URL based on content type
        if is_tv:
            search_url = f"https://vixsrc.to/search?keyword={query}&vrf=&vt=tv"
        else:
            search_url = f"https://vixsrc.to/search?keyword={query}&vrf=&vt=movie"

        # Send the request con sessione ottimizzata
        response = http_session.get(search_url, timeout=15)

        if response.status_code != 200:
            print(f"Error: Failed to search vixsrc.to (status code: {response.status_code})", file=sys.stderr)
            return None

        # Parse the HTML response - ottimizzato
        soup = BeautifulSoup(response.text, 'html.parser')

        # Find all movie/show cards - parsing più veloce
        cards = soup.select('.flw-item')

        results = []
        # Limita il numero di risultati da processare per velocità
        for card in cards[:10]:  # Solo i primi 10 risultati
            try:
                # Extract the title
                title_elem = card.select_one('.film-name a')
                if not title_elem:
                    continue

                result_title = title_elem.text.strip()

                # Extract the URL and ID
                url = title_elem.get('href', '')
                if not url:
                    continue

                # Extract the ID from the URL - regex ottimizzata
                if is_tv:
                    id_match = re.search(r'/tv/(\d+)', url)
                else:
                    id_match = re.search(r'/movie/(\d+)', url)

                if not id_match:
                    continue

                result_id = id_match.group(1)

                # Extract the year if available - parsing più veloce
                year_elem = card.select_one('.fd-infor .fdi-item')
                result_year = None
                if year_elem:
                    year_text = year_elem.get_text(strip=True)
                    year_match = re.search(r'(\d{4})', year_text)
                    if year_match:
                        result_year = year_match.group(1)

                # Add to results
                results.append({
                    'id': result_id,
                    'title': result_title,
                    'year': result_year,
                    'url': url
                })

                # Se abbiamo un match esatto per anno, fermati qui
                if year and result_year == str(year):
                    break

            except Exception as e:
                print(f"Error parsing result: {str(e)}", file=sys.stderr)
                continue

        # Determina il risultato migliore
        selected_result = None

        # Se abbiamo risultati e un anno, cerca match esatto
        if results and year:
            for result in results:
                if result['year'] == str(year):
                    selected_result = result['id']
                    break

        # Altrimenti usa il primo risultato
        if not selected_result and results:
            selected_result = results[0]['id']

        # Se non abbiamo trovato risultati ma abbiamo un TMDB ID, usalo direttamente
        if not selected_result and tmdb_id:
            print(f"No search results found, using TMDB ID {tmdb_id} directly", file=sys.stderr)
            selected_result = tmdb_id

        # Salva in cache se abbiamo un risultato
        if selected_result:
            cache_data[search_cache_key] = selected_result
            if tmdb_id:
                cache_data[get_cache_key_tmdb(tmdb_id, is_tv)] = selected_result
            save_cache_tmdb()

        return selected_result
    
    except Exception as e:
        print(f"Error searching vixsrc.to: {str(e)}", file=sys.stderr)
        # Se c'è un errore ma abbiamo un TMDB ID, proviamo a usarlo direttamente
        if tmdb_id:
            print(f"Search failed, using TMDB ID {tmdb_id} directly", file=sys.stderr)
            return tmdb_id
        return None

# Function to get movie/TV show details from TMDB
def get_tmdb_details(tmdb_id, api_key, is_tv=False):
    try:
        # Controlla cache per dettagli TMDB
        details_cache_key = f"tmdb_details_{tmdb_id}_{is_tv}"
        if details_cache_key in cache_data:
            print("Using cached TMDB details", file=sys.stderr)
            return cache_data[details_cache_key]

        # Determine the API endpoint based on content type
        if is_tv:
            url = f"https://api.themoviedb.org/3/tv/{tmdb_id}?api_key={api_key}"
        else:
            url = f"https://api.themoviedb.org/3/movie/{tmdb_id}?api_key={api_key}"

        # Send the request con sessione ottimizzata
        response = http_session.get(url, timeout=10)

        if response.status_code != 200:
            print(f"Error: Failed to get TMDB details (status code: {response.status_code})", file=sys.stderr)
            return None

        # Parse the JSON response
        data = response.json()

        # Extract the title and year - ottimizzato
        if is_tv:
            title = data.get('name')
            first_air_date = data.get('first_air_date', '')
            year = first_air_date[:4] if first_air_date and len(first_air_date) >= 4 else None
        else:
            title = data.get('title')
            release_date = data.get('release_date', '')
            year = release_date[:4] if release_date and len(release_date) >= 4 else None

        result = {
            'title': title,
            'year': year
        }

        # Salva in cache
        cache_data[details_cache_key] = result
        save_cache_tmdb()

        return result

    except Exception as e:
        print(f"Error getting TMDB details: {str(e)}", file=sys.stderr)
        return None

# Main function to convert TMDB ID to vixsrc ID
def tmdb_to_vixsrc(tmdb_id, api_key, is_tv=False):
    # Carica cache
    load_cache_tmdb()

    # Controlla cache per conversione completa
    conversion_cache_key = f"conversion_{tmdb_id}_{is_tv}"
    if conversion_cache_key in cache_data:
        print("Using cached conversion result", file=sys.stderr)
        return cache_data[conversion_cache_key]

    # Get details from TMDB
    details = get_tmdb_details(tmdb_id, api_key, is_tv)

    if not details or not details['title']:
        print(f"Error: Failed to get details for TMDB ID {tmdb_id}", file=sys.stderr)
        return None

    # Search vixsrc.to for the title and year, passing the TMDB ID as fallback
    vixsrc_id = search_vixsrc(details['title'], details['year'], is_tv, tmdb_id)

    # Salva risultato conversione in cache
    if vixsrc_id:
        cache_data[conversion_cache_key] = vixsrc_id
        save_cache_tmdb()

    return vixsrc_id

# Command-line interface
def main():
    parser = argparse.ArgumentParser(description='Convert TMDB ID to vixsrc ID')
    parser.add_argument('tmdb_id', help='TMDB ID')
    parser.add_argument('--api-key', required=True, help='TMDB API key')
    parser.add_argument('--tv', action='store_true', help='Treat as TV show')
    parser.add_argument('--season', type=int, help='Season number for TV shows')
    parser.add_argument('--episode', type=int, help='Episode number for TV shows')
    
    args = parser.parse_args()
    
    vixsrc_id = tmdb_to_vixsrc(args.tmdb_id, args.api_key, args.tv)
    
    if vixsrc_id:
        # Output as JSON for easy parsing
        result = {
            'tmdb_id': args.tmdb_id,
            'vixsrc_id': vixsrc_id,
            'is_tv': args.tv,
            'season': args.season,
            'episode': args.episode
        }
        print(json.dumps(result))
        return 0
    else:
        print(json.dumps({'error': f"Failed to convert TMDB ID {args.tmdb_id} to vixsrc ID"}))
        return 1

if __name__ == "__main__":
    sys.exit(main())