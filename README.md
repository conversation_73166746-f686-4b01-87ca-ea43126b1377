# Stremio Vixsrc Addon

Questo addon per Stremio permette di guardare film e serie TV da vixsrc.to direttamente nell'app Stremio.

## Funzionalità

- Integrazione con Stremio per lo streaming di film e serie TV
- Conversione automatica da ID IMDB a ID TMDB
- Ricerca automatica dei contenuti su vixsrc.to
- Estrazione e streaming dei flussi m3u8

## Requisiti

- Node.js (v12 o superiore)
- Python 3.6 o superiore
- Pacchetti Python richiesti (installabili con pip):
  - undetected-chromedriver
  - selenium
  - requests
  - beautifulsoup4
- Chrome o Chromium installato
- Una chiave API di TMDB (gratuita)
- Per Raspberry Pi: chromium-chromedriver

## Installazione

1. Clona o scarica questo repository

2. Installa le dipendenze Node.js:

```bash
cd stremio-vixsrc-addon
npm install
```

3. Installa le dipendenze Python:

```bash
pip install undetected-chromedriver selenium requests beautifulsoup4
```

4. Configura la tua chiave API TMDB:

Ottieni una chiave API gratuita da [TMDB](https://www.themoviedb.org/settings/api) e impostala come variabile d'ambiente:

```bash
export TMDB_API_KEY=your_api_key_here
```

Oppure modifica direttamente il file `index.js` sostituendo `YOUR_TMDB_API_KEY` con la tua chiave API.

## Configurazione per Raspberry Pi

1. Installa chromium-chromedriver:

```bash
sudo apt-get update
sudo apt-get install chromium-chromedriver
```

2. Sostituisci il file `video_extractor.py` con `video_extractor_raspberry.py`:

```bash
mv video_extractor_raspberry.py video_extractor.py
```

## Configurazione di ngrok (per accesso remoto)

1. Installa ngrok seguendo la guida ufficiale: [ngrok Setup Guide](https://dashboard.ngrok.com/get-started/setup/)

2. Configura l'auth token di ngrok:

```bash
ngrok config add-authtoken $YOUR_AUTHTOKEN
```

3. Configura un dominio statico:
   - Vai su [ngrok Domains](https://dashboard.ngrok.com/domains)
   - Segui la procedura guidata
   - Copia l'URL del dominio generato

4. Crea o modifica il file `ngrok.yml` con il seguente contenuto:

```yaml
version: 2
authtoken: TUO_TOKEN
vixsrc:
  stremio-addon:
    proto: http
    addr: 7000
    domain: DOMINIO_NGROK
```

5. Avvia ngrok con il comando:

```bash
ngrok start vixsrc
```

## Utilizzo

1. Avvia l'addon:

```bash
npm start
```

2. L'addon sarà disponibile all'indirizzo http://127.0.0.1:7000 (o tramite il tuo dominio ngrok se configurato)

3. Installa l'addon in Stremio:
   - Apri Stremio
   - Vai su Addon -> Addon Community
   - Clicca su "Addon Community"
   - Inserisci l'URL dell'addon (http://127.0.0.1:7000/manifest.json o il tuo URL ngrok)
   - Clicca su "Installa"

4. Ora puoi cercare film e serie TV in Stremio e riprodurli tramite l'addon Vixsrc

## Come funziona

1. Quando selezioni un film o una serie TV in Stremio, l'app invia una richiesta all'addon con l'ID IMDB del contenuto
2. L'addon utilizza l'API di IMDB per ottenere il titolo e l'anno del contenuto
3. L'ID IMDB viene convertito in ID TMDB utilizzando l'API di TMDB
4. L'addon cerca il contenuto su vixsrc.to utilizzando il titolo e l'anno
5. Una volta trovato il contenuto, l'addon estrae l'URL del flusso m3u8
6. L'URL del flusso viene restituito a Stremio per la riproduzione

## Limitazioni

- L'addon funziona solo quando è in esecuzione sul tuo computer locale (a meno che non si utilizzi ngrok)
- È necessario avere Chrome o Chromium installato
- La ricerca su vixsrc.to potrebbe non trovare tutti i contenuti
- L'estrazione del flusso m3u8 potrebbe non funzionare per tutti i contenuti

## Risoluzione dei problemi

- Se l'addon non trova il contenuto, prova a cercarlo manualmente su vixsrc.to
- Se l'estrazione del flusso m3u8 fallisce, prova a riavviare l'addon
- Controlla i log dell'addon per eventuali errori

## Note legali

Questo addon è solo a scopo educativo. L'utilizzo di questo addon per accedere a contenuti protetti da copyright potrebbe violare le leggi sul copyright nel tuo paese. Utilizza questo addon solo per accedere a contenuti di pubblico dominio o per i quali hai i diritti necessari.