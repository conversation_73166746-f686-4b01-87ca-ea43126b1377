import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import re
import json
import argparse
import sys

def extract_stream_url(video_id, is_tv=False, season=None, episode=None):
    try:
        if is_tv:
            if '/' in video_id:
                url = f"https://vixsrc.to/tv/{video_id}/"
            else:
                season = season or 1
                episode = episode or 1
                url = f"https://vixsrc.to/tv/{video_id}/{season}/{episode}/"
        else:
            url = f"https://vixsrc.to/movie/{video_id}/"

        print(f"Accessing URL: {url}", file=sys.stderr)

        options = uc.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.page_load_strategy = 'eager'

        options.set_capability('goog:loggingPrefs', {
            'performance': 'ALL',
            'browser': 'ALL',
            'network': 'ALL'
        })

        driver = uc.Chrome(driver_executable_path='/usr/bin/chromedriver', options=options)

        try:
            driver.get(url)

            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "iframe[src*='player'], .jwplayer"))
            )

            # Switch to first iframe (if any)
            iframes = driver.find_elements(By.CSS_SELECTOR, "iframe[src*='player'], iframe[src*='embed']")
            for iframe in iframes:
                try:
                    driver.switch_to.frame(iframe)
                    break
                except Exception:
                    continue

            time.sleep(5)  # Attendi caricamento

            logs = driver.get_log('performance')
            media_urls = []

            for log in logs:
                try:
                    message = json.loads(log['message'])['message']
                    if message.get('method') == 'Network.responseReceived':
                        response = message['params']['response']
                        url_found = response.get('url', '')
                        mime_type = response.get('mimeType', '')
                        if '.m3u8' in url_found or 'mpegurl' in mime_type.lower():
                            if url_found not in media_urls:
                                media_urls.append(url_found)
                except Exception:
                    continue

            if not media_urls:
                page_source = driver.page_source
                urls = re.findall(r'https?://[^\s\'\"]+\.m3u8[^\s\'\"]*', page_source)
                media_urls.extend([u for u in urls if u not in media_urls])

            return media_urls[0] if media_urls else None

        finally:
            driver.quit()

    except Exception as e:
        print(f"Error extracting video: {e}", file=sys.stderr)
        return None

def main():
    parser = argparse.ArgumentParser(description='Extract m3u8 URL from vixsrc.to')
    parser.add_argument('video_id', help='Video ID or URL')
    parser.add_argument('--tv', action='store_true')
    parser.add_argument('--movie', action='store_true')
    parser.add_argument('--extract-only', action='store_true')
    parser.add_argument('--season', type=int)
    parser.add_argument('--episode', type=int)
    args = parser.parse_args()

    video_id = args.video_id
    is_tv = args.tv

    if video_id.startswith('http'):
        if 'vixsrc.to/tv/' in video_id:
            is_tv = True
            video_id = video_id.split('vixsrc.to/tv/')[1].rstrip('/')
        elif 'vixsrc.to/movie/' in video_id:
            is_tv = False
            video_id = video_id.split('vixsrc.to/movie/')[1].rstrip('/')

    if '/' in video_id and not args.movie:
        is_tv = True

    if args.movie:
        is_tv = False

    result = extract_stream_url(video_id, is_tv, args.season, args.episode)

    if args.extract_only:
        if result:
            if not result.endswith('.m3u8'):
                result += '.m3u8'
            print(result)
            return 0
        else:
            print("Failed to extract URL", file=sys.stderr)
            return 1
    else:
        if result:
            print(f"Found stream URL: {result}")
            return 0
        else:
            print("No stream URL found.", file=sys.stderr)
            return 1

if __name__ == "__main__":
    sys.exit(main())