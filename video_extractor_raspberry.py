import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import re
import json
import argparse
import sys
import hashlib
import pickle
from concurrent.futures import ThreadPoolExecutor

# Cache per Raspberry Pi
CACHE_FILE_RPI = '/tmp/vixsrc_rpi_cache.pkl'
url_cache_rpi = {}

def load_cache_rpi():
    global url_cache_rpi
    try:
        with open(CACHE_FILE_RPI, 'rb') as f:
            url_cache_rpi = pickle.load(f)
    except (FileNotFoundError, EOFError):
        url_cache_rpi = {}

def save_cache_rpi():
    try:
        # Limita dimensione cache per RPI (memoria limitata)
        if len(url_cache_rpi) > 200:
            items = list(url_cache_rpi.items())
            url_cache_rpi.clear()
            url_cache_rpi.update(items[-100:])

        with open(CACHE_FILE_RPI, 'wb') as f:
            pickle.dump(url_cache_rpi, f)
    except Exception:
        pass

def get_cache_key_rpi(video_id, is_tv, season, episode):
    return hashlib.md5(f"{video_id}_{is_tv}_{season}_{episode}".encode()).hexdigest()

def extract_stream_url(video_id, is_tv=False, season=None, episode=None):
    try:
        # Carica cache
        load_cache_rpi()

        # Controlla cache
        cache_key = get_cache_key_rpi(video_id, is_tv, season, episode)
        if cache_key in url_cache_rpi:
            print("Using cached URL", file=sys.stderr)
            return url_cache_rpi[cache_key]

        if is_tv:
            if '/' in video_id:
                url = f"https://vixsrc.to/tv/{video_id}/"
            else:
                season = season or 1
                episode = episode or 1
                url = f"https://vixsrc.to/tv/{video_id}/{season}/{episode}/"
        else:
            url = f"https://vixsrc.to/movie/{video_id}/"

        print(f"Accessing URL: {url}", file=sys.stderr)

        # Opzioni ottimizzate per Raspberry Pi
        options = uc.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-images')
        options.add_argument('--disable-javascript')
        options.add_argument('--memory-pressure-off')
        options.add_argument('--max_old_space_size=512')  # Limita memoria per RPI
        options.page_load_strategy = 'eager'

        options.set_capability('goog:loggingPrefs', {
            'performance': 'ALL',
            'network': 'ALL'
        })

        driver = uc.Chrome(driver_executable_path='/usr/bin/chromedriver', options=options)

        try:
            driver.get(url)

            # Timeout ridotto per RPI
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "iframe[src*='player'], .jwplayer"))
            )

            # Attesa ridotta
            time.sleep(2)

            # Prima prova con i log di performance
            logs = driver.get_log('performance')
            media_urls = []

            for log in logs:
                try:
                    message = json.loads(log['message'])['message']
                    if message.get('method') == 'Network.responseReceived':
                        response = message['params']['response']
                        url_found = response.get('url', '')
                        mime_type = response.get('mimeType', '')
                        if '.m3u8' in url_found or 'mpegurl' in mime_type.lower():
                            if url_found not in media_urls:
                                media_urls.append(url_found)
                except Exception:
                    continue

            # Se non trova nulla, prova con iframe (solo il primo)
            if not media_urls:
                iframes = driver.find_elements(By.CSS_SELECTOR, "iframe[src*='player'], iframe[src*='embed']")
                if iframes:
                    try:
                        driver.switch_to.frame(iframes[0])  # Solo il primo iframe
                        time.sleep(1)

                        iframe_logs = driver.get_log('performance')
                        for log in iframe_logs:
                            try:
                                message = json.loads(log['message'])['message']
                                if message.get('method') == 'Network.responseReceived':
                                    response = message['params']['response']
                                    url_found = response.get('url', '')
                                    if '.m3u8' in url_found:
                                        media_urls.append(url_found)
                            except Exception:
                                continue

                        driver.switch_to.default_content()
                    except Exception:
                        driver.switch_to.default_content()

            # Fallback: page source
            if not media_urls:
                page_source = driver.page_source
                urls = re.findall(r'https?://[^\s\'\"]+\.m3u8[^\s\'\"]*', page_source)
                media_urls.extend([u for u in urls if u not in media_urls])

            # Seleziona il miglior URL e salva in cache
            if media_urls:
                selected_url = media_urls[0]
                for url in media_urls:
                    if 'master.m3u8' in url or 'index.m3u8' in url:
                        selected_url = url
                        break

                # Salva in cache
                url_cache_rpi[cache_key] = selected_url
                save_cache_rpi()

                return selected_url

            return None

        finally:
            driver.quit()

    except Exception as e:
        print(f"Error extracting video: {e}", file=sys.stderr)
        return None

def main():
    parser = argparse.ArgumentParser(description='Extract m3u8 URL from vixsrc.to')
    parser.add_argument('video_id', help='Video ID or URL')
    parser.add_argument('--tv', action='store_true')
    parser.add_argument('--movie', action='store_true')
    parser.add_argument('--extract-only', action='store_true')
    parser.add_argument('--season', type=int)
    parser.add_argument('--episode', type=int)
    args = parser.parse_args()

    video_id = args.video_id
    is_tv = args.tv

    if video_id.startswith('http'):
        if 'vixsrc.to/tv/' in video_id:
            is_tv = True
            video_id = video_id.split('vixsrc.to/tv/')[1].rstrip('/')
        elif 'vixsrc.to/movie/' in video_id:
            is_tv = False
            video_id = video_id.split('vixsrc.to/movie/')[1].rstrip('/')

    if '/' in video_id and not args.movie:
        is_tv = True

    if args.movie:
        is_tv = False

    result = extract_stream_url(video_id, is_tv, args.season, args.episode)

    if args.extract_only:
        if result:
            if not result.endswith('.m3u8'):
                result += '.m3u8'
            print(result)
            return 0
        else:
            print("Failed to extract URL", file=sys.stderr)
            return 1
    else:
        if result:
            print(f"Found stream URL: {result}")
            return 0
        else:
            print("No stream URL found.", file=sys.stderr)
            return 1

if __name__ == "__main__":
    sys.exit(main())