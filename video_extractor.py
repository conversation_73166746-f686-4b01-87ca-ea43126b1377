import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver import ChromeOptions
import time
import os
import re
import json
import subprocess
import argparse
import sys
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import hashlib
import pickle

# Cache per evitare richieste ripetute
CACHE_FILE = '/tmp/vixsrc_cache.pkl'
url_cache = {}

def load_cache():
    global url_cache
    try:
        with open(CACHE_FILE, 'rb') as f:
            url_cache = pickle.load(f)
    except (FileNotFoundError, EOFError):
        url_cache = {}

def save_cache():
    try:
        # Limita dimensione cache
        if len(url_cache) > 500:
            items = list(url_cache.items())
            url_cache.clear()
            url_cache.update(items[-250:])

        with open(CACHE_FILE, 'wb') as f:
            pickle.dump(url_cache, f)
    except Exception:
        pass

def get_cache_key(video_id, is_tv, season, episode):
    return hashlib.md5(f"{video_id}_{is_tv}_{season}_{episode}".encode()).hexdigest()

def stream_video(video_id, is_tv=False, extract_only=False, season=None, episode=None):
    try:
        # Carica cache
        load_cache()

        # Controlla cache
        cache_key = get_cache_key(video_id, is_tv, season, episode)
        if cache_key in url_cache:
            print("Using cached URL", file=sys.stderr)
            cached_url = url_cache[cache_key]
            if extract_only:
                return cached_url
            # Continua con VLC se non è extract_only

        # Construct URL based on content type (movie or TV show)
        if is_tv:
            # For TV shows, check if the video_id contains season/episode info
            if '/' in video_id:
                # Format is already vixsrcId/season/episode
                url = f"https://vixsrc.to/tv/{video_id}/"
            else:
                # No season/episode info in video_id, use season and episode parameters if available
                if season is not None and episode is not None:
                    url = f"https://vixsrc.to/tv/{video_id}/{season}/{episode}/"
                else:
                    # Default to season 1, episode 1 if no season/episode info is provided
                    url = f"https://vixsrc.to/tv/{video_id}/1/1/"
        else:
            url = f"https://vixsrc.to/movie/{video_id}/"

        print(f"Accessing URL: {url}", file=sys.stderr)

        # Configure Chrome options - ottimizzate per performance
        options = uc.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-images')
        options.add_argument('--disable-javascript')  # Disabilita JS inizialmente
        options.add_argument('--aggressive-cache-discard')
        options.add_argument('--memory-pressure-off')
        options.page_load_strategy = 'eager'  # Non aspetta il caricamento completo

        # Enable performance logging
        options.set_capability('goog:loggingPrefs', {
            'performance': 'ALL',
            'network': 'ALL'
        })

        # Initialize undetected-chromedriver
        driver = uc.Chrome(options=options)
        
        try:
            # Navigate to the URL
            driver.get(url)
            print("Page loaded, waiting for video player...")

            # Wait for the video player to load - ridotto timeout
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "video, iframe[src*='player'], #player, .jwplayer"))
            )

            print("Video player found, looking for media URLs...")
            time.sleep(3)  # Ridotto da 10 a 3 secondi

            # Get performance logs to find media URLs - prima di iframe switching
            logs = driver.get_log('performance')
            media_urls = []

            # Process performance logs to find network requests
            for log in logs:
                try:
                    log_entry = json.loads(log.get('message', '{}'))
                    message = log_entry.get('message', {})

                    # Check for network responses
                    if message.get('method') == 'Network.responseReceived':
                        response = message.get('params', {}).get('response', {})
                        url_found = response.get('url', '')
                        mime_type = response.get('mimeType', '')

                        # Look for m3u8 playlists or media segments
                        if '.m3u8' in url_found or 'mpegurl' in mime_type.lower():
                            if url_found not in media_urls:
                                media_urls.append(url_found)
                                print(f"Found m3u8 URL in network logs: {url_found}")
                except Exception:
                    continue

            # Se non abbiamo trovato URL, prova con iframe
            if not media_urls:
                print("No URLs found in initial logs, checking iframes...")
                iframes = driver.find_elements(By.CSS_SELECTOR, "iframe[src*='player'], iframe[src*='embed']")

                def check_iframe(iframe):
                    try:
                        driver.switch_to.frame(iframe)
                        time.sleep(2)  # Ridotto da 3 a 2 secondi

                        # Riabilita JavaScript per iframe
                        driver.execute_script("document.documentElement.style.display = 'block';")

                        # Get logs from iframe
                        iframe_logs = driver.get_log('performance')
                        iframe_urls = []

                        for log in iframe_logs:
                            try:
                                log_entry = json.loads(log.get('message', '{}'))
                                message = log_entry.get('message', {})

                                if message.get('method') == 'Network.responseReceived':
                                    response = message.get('params', {}).get('response', {})
                                    url_found = response.get('url', '')
                                    mime_type = response.get('mimeType', '')

                                    if '.m3u8' in url_found or 'mpegurl' in mime_type.lower():
                                        iframe_urls.append(url_found)
                            except Exception:
                                continue

                        driver.switch_to.default_content()
                        return iframe_urls
                    except Exception:
                        driver.switch_to.default_content()
                        return []

                # Controlla iframe in parallelo (limitato a 2 per evitare sovraccarico)
                with ThreadPoolExecutor(max_workers=min(2, len(iframes))) as executor:
                    future_to_iframe = {executor.submit(check_iframe, iframe): iframe for iframe in iframes[:2]}
                    for future in as_completed(future_to_iframe):
                        iframe_urls = future.result()
                        media_urls.extend([url for url in iframe_urls if url not in media_urls])

            
            # Se ancora non abbiamo URL, prova page source (più veloce)
            if not media_urls:
                print("No media URLs found in logs, checking page source...")
                page_source = driver.page_source
                urls = re.findall(r'https?://[^\s\'\"]+\.m3u8[^\s\'\"]*', page_source)
                media_urls.extend([url for url in urls if url not in media_urls])
                if media_urls:
                    print(f"Found {len(media_urls)} m3u8 URLs in page source")

            # Solo come ultima risorsa, usa JavaScript (più lento)
            if not media_urls:
                print("No media URLs found in page source, trying JavaScript extraction...")
                try:
                    # JavaScript ottimizzato - solo pattern essenziali
                    js_urls = driver.execute_script("""
                    const sources = [];

                    // Check video elements
                    document.querySelectorAll('video').forEach(video => {
                        if (video.src && video.src.includes('.m3u8')) sources.push(video.src);
                        video.querySelectorAll('source').forEach(source => {
                            if (source.src && source.src.includes('.m3u8')) sources.push(source.src);
                        });
                    });

                    // Check common variables
                    ['player_source', 'playerSource', 'videoSource', 'streamUrl', 'hlsUrl'].forEach(varName => {
                        if (window[varName] && typeof window[varName] === 'string' && window[varName].includes('.m3u8')) {
                            sources.push(window[varName]);
                        }
                    });

                    // Quick script scan
                    const scriptText = Array.from(document.scripts).map(s => s.textContent).join(' ');
                    const matches = scriptText.match(/"(https?:\/\/[^"]+\.m3u8[^"]*)"/g);
                    if (matches) sources.push(...matches.map(m => m.slice(1, -1)));

                    return [...new Set(sources)];
                    """)

                    if js_urls:
                        media_urls.extend([url for url in js_urls if url and not url.startswith('blob:') and url not in media_urls])
                        print(f"Found {len(js_urls)} m3u8 URLs via JavaScript")
                except Exception as e:
                    print(f"JavaScript extraction failed: {str(e)}")

            # Check if we found any media URLs
            if not media_urls:
                print("No media URLs found")
                return False

            print(f"Found {len(media_urls)} media URLs")

            # Seleziona il miglior URL (preferisci master.m3u8 o index.m3u8)
            selected_url = media_urls[0]
            for url in media_urls:
                if 'master.m3u8' in url or 'index.m3u8' in url:
                    selected_url = url
                    break

            print(f"Selected URL: {selected_url}")

            # Salva in cache
            url_cache[cache_key] = selected_url
            save_cache()

            # If extract_only is True, just return the URL
            if extract_only:
                return selected_url
            
            # Launch VLC with the media URL
            try:
                vlc_cmd = [
                    '/Applications/VLC.app/Contents/MacOS/VLC',
                    '--network-caching=10000',  # Increased network caching (10 seconds)
                    '--live-caching=10000',     # Cache for live streams
                    '--file-caching=10000',     # Cache for files
                    '--disc-caching=10000',     # Cache for discs
                    '--clock-jitter=0',         # Reduce clock jitter
                    '--clock-synchro=0',        # Clock synchronization
                    '--prefetch-buffer-size=1048576', # Prefetch buffer size (1MB)
                    selected_url
                ]
                
                print("Launching VLC...")
                subprocess.Popen(vlc_cmd)
                print("VLC launched successfully. Streaming video...")
                print("Press Ctrl+C to exit when done watching")
                
                # Keep the script running until user interrupts
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\nScript interrupted by user. Exiting...")
                
                return True
                
            except Exception as e:
                print(f"Error launching VLC: {str(e)}")
                return False
                
        finally:
            driver.quit()

    except Exception as e:
        print(f"Error streaming video: {str(e)}")
        if 'driver' in locals():
            driver.quit()
        return False

def main():
    parser = argparse.ArgumentParser(description='Stream video from vixsrc.to')
    parser.add_argument('video_id', help='Video ID from vixsrc.to')
    parser.add_argument('--tv', action='store_true', help='Treat as TV show')
    parser.add_argument('--movie', action='store_true', help='Treat as movie')
    parser.add_argument('--extract-only', action='store_true', help='Only extract the streaming URL, don\'t launch VLC')
    parser.add_argument('--season', type=int, help='Season number for TV shows')
    parser.add_argument('--episode', type=int, help='Episode number for TV shows')
    
    args = parser.parse_args()
    
    video_id = args.video_id
    is_tv = args.tv
    
    # If the video_id contains a URL, extract the ID
    if video_id.startswith('http'):
        # Extract the ID from the URL
        if 'vixsrc.to/tv/' in video_id:
            is_tv = True
            video_id = video_id.split('vixsrc.to/tv/')[1].rstrip('/')
        elif 'vixsrc.to/movie/' in video_id:
            is_tv = False
            video_id = video_id.split('vixsrc.to/movie/')[1].rstrip('/')
    
    # If the video_id contains slashes, it's likely a TV show with season/episode
    if '/' in video_id and not args.movie:
        is_tv = True
        print(f"Detected TV show format: {video_id}", file=sys.stderr)
    
    # Override with --movie flag if specified
    if args.movie:
        is_tv = False
    
    result = stream_video(video_id, is_tv, args.extract_only, args.season, args.episode)
    
    if args.extract_only:
        if result:
            # Ensure the URL has .m3u8 extension
            if not result.endswith('.m3u8'):
                # Simply add .m3u8 extension to the URL
                result = result + '.m3u8'
                print(f"Added .m3u8 extension to URL: {result}", file=sys.stderr)
            
            # Only write the URL to stdout for easy capture by other programs
            # Don't print with a label to avoid parsing issues
            sys.stdout.write(result)
            return 0
        else:
            print("Failed to extract URL!", file=sys.stderr)
            return 1
    else:
        if result:
            print("Streaming completed!")
            return 0
        else:
            print("Streaming failed!")
            return 1

if __name__ == "__main__":
    sys.exit(main())