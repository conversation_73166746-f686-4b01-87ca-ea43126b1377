import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver import ChromeOptions
import time
import os
import re
import json
import subprocess
import argparse
import sys

def stream_video(video_id, is_tv=False, extract_only=False, season=None, episode=None):
    try:
        # Construct URL based on content type (movie or TV show)
        if is_tv:
            # For TV shows, check if the video_id contains season/episode info
            if '/' in video_id:
                # Format is already vixsrcId/season/episode
                url = f"https://vixsrc.to/tv/{video_id}/"
            else:
                # No season/episode info in video_id, use season and episode parameters if available
                if season is not None and episode is not None:
                    url = f"https://vixsrc.to/tv/{video_id}/{season}/{episode}/"
                else:
                    # Default to season 1, episode 1 if no season/episode info is provided
                    url = f"https://vixsrc.to/tv/{video_id}/1/1/"
        else:
            url = f"https://vixsrc.to/movie/{video_id}/"
            
        print(f"Accessing URL: {url}", file=sys.stderr)

        # Configure Chrome options
        options = uc.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        # Enable performance logging
        options.set_capability('goog:loggingPrefs', {
            'performance': 'ALL',
            'browser': 'ALL',
            'network': 'ALL'
        })

        # Initialize undetected-chromedriver
        driver = uc.Chrome(options=options)
        
        try:
            # Navigate to the URL
            driver.get(url)
            print("Page loaded, waiting for video player...")
            
            # Wait for the video player to load
            WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "video, iframe[src*='player'], #player, .jwplayer"))
            )
            
            print("Video player found, looking for media URLs...")
            time.sleep(10)  # Give more time for network requests to complete
            
            # Check for iframes and switch to them if needed
            iframes = driver.find_elements(By.CSS_SELECTOR, "iframe[src*='player'], iframe[src*='embed']") 
            if iframes:
                print(f"Found {len(iframes)} player iframes, checking them...")
                for iframe in iframes:
                    try:
                        print(f"Switching to iframe: {iframe.get_attribute('src')}")
                        driver.switch_to.frame(iframe)
                        time.sleep(3)  # Wait for iframe content to load
                        # Continue with extraction in the iframe context
                    except Exception as e:
                        print(f"Error switching to iframe: {str(e)}")
                        driver.switch_to.default_content()
            
            # Get performance logs to find media URLs
            logs = driver.get_log('performance')
            
            # Look for m3u8 URLs in the logs
            media_urls = []
            
            # Process performance logs to find network requests
            for log in logs:
                try:
                    log_entry = json.loads(log.get('message', '{}'))
                    message = log_entry.get('message', {})
                    
                    # Check for network responses
                    if message.get('method') == 'Network.responseReceived':
                        response = message.get('params', {}).get('response', {})
                        url_found = response.get('url', '')
                        mime_type = response.get('mimeType', '')
                        
                        # Look for m3u8 playlists or media segments
                        if '.m3u8' in url_found or 'mpegurl' in mime_type.lower():
                            if url_found not in media_urls:
                                media_urls.append(url_found)
                                print(f"Found m3u8 URL in network logs: {url_found}")
                        
                        # Look for ts segments which might indicate an HLS stream
                        elif '.ts' in url_found and 'video' in mime_type.lower():
                            # Extract the base URL to find the playlist
                            base_url = url_found.rsplit('/', 1)[0]
                            potential_playlist = f"{base_url}/index.m3u8"
                            if potential_playlist not in media_urls:
                                media_urls.append(potential_playlist)
                                print(f"Found potential playlist from segment: {potential_playlist}")
                except Exception as e:
                    continue
            
            # If no URLs found in logs, try to extract from page source
            if not media_urls:
                print("No media URLs found in logs, checking page source...")
                page_source = driver.page_source
                urls = re.findall(r'https?://[^\s\'\"]+\.m3u8[^\s\'\"]*', page_source)
                for url in urls:
                    if url not in media_urls:
                        media_urls.append(url)
                        print(f"Found m3u8 URL in page source: {url}")
            
            # If still no URLs, try JavaScript execution with more patterns
            if not media_urls:
                print("No media URLs found in page source, executing JavaScript...")
                # Try to extract from video element and player configurations
                try:
                    js_urls = driver.execute_script("""
                    function findVideoSources() {
                        const sources = [];
                        
                        // Check video elements
                        const videos = document.querySelectorAll('video');
                        for (const video of videos) {
                            if (video.src && video.src.includes('\.m3u8')) {
                                sources.push(video.src);
                            }
                            
                            // Check source elements inside video
                            const videoSources = video.querySelectorAll('source');
                            for (const source of videoSources) {
                                if (source.src && source.src.includes('\.m3u8')) {
                                    sources.push(source.src);
                                }
                            }
                        }
                        
                        // Check for JW Player
                        if (typeof jwplayer !== 'undefined') {
                            const players = jwplayer();
                            if (players) {
                                const config = players.getConfig();
                                if (config && config.file) {
                                    sources.push(config.file);
                                }
                            }
                        }
                        
                        // Check for common player variables
                        const playerVars = ['player_source', 'playerSource', 'videoSource', 'video_source', 'streamUrl', 'stream_url', 'hlsUrl', 'hls_url'];
                        for (const varName of playerVars) {
                            if (window[varName] && typeof window[varName] === 'string' && window[varName].includes('.m3u8')) {
                                sources.push(window[varName]);
                            }
                        }
                        
                        // Check script tags for playlist URLs
                        const scripts = document.querySelectorAll('script');
                        const patterns = [
                            /source[ ]*:[ ]*['"](.+?\.m3u8[^'"]*)['"]/, 
                            /file[ ]*:[ ]*['"](.+?\.m3u8[^'"]*)['"]/, 
                            /src[ ]*:[ ]*['"](.+?\.m3u8[^'"]*)['"]/, 
                            /url[ ]*:[ ]*['"](.+?\.m3u8[^'"]*)['"]/, 
                            /hls[ ]*:[ ]*['"](.+?\.m3u8[^'"]*)['"]/, 
                            /playlist[ ]*:[ ]*['"](.+?\.m3u8[^'"]*)['"]/, 
                            /m3u8[ ]*:[ ]*['"](.+?\.m3u8[^'"]*)['"]/, 
                            /master\.m3u8[ ]*:[ ]*['"](.+?\.m3u8[^'"]*)['"]/, 
                            /"(https?:\/\/[^"]+\.m3u8[^"]*)"/,
                            /'(https?:\/\/[^']+\.m3u8[^']*)'/
                        ];
                        
                        for (const script of scripts) {
                            const content = script.textContent || '';
                            for (const pattern of patterns) {
                                const match = content.match(pattern);
                                if (match && match[1]) {
                                    sources.push(match[1]);
                                }
                            }
                        }
                        
                        return sources;
                    }
                    return findVideoSources();
                    """)
                    
                    if js_urls and len(js_urls) > 0:
                        for url in js_urls:
                            if url and url not in media_urls and not url.startswith('blob:'):
                                media_urls.append(url)
                                print(f"Found m3u8 URL via JavaScript: {url}")
                except Exception as e:
                    print(f"Error executing JavaScript: {str(e)}")
            
            # If we're in an iframe, switch back to main content
            if iframes:
                driver.switch_to.default_content()
                print("Switched back to main content")
            
            # Check if we found any media URLs
            if not media_urls:
                print("No media URLs found")
                return False
            
            print(f"Found {len(media_urls)} media URLs")
            for i, url in enumerate(media_urls):
                print(f"[{i+1}] {url}")
            
            # Use the first URL or let user choose
            if len(media_urls) == 1:
                selected_url = media_urls[0]
            else:
                print("\nMultiple media URLs found. Using the first one by default.")
                selected_url = media_urls[0]
            
            print(f"\nStreaming URL: {selected_url}")
            
            # If extract_only is True, just return the URL
            if extract_only:
                return selected_url
            
            # Launch VLC with the media URL
            try:
                vlc_cmd = [
                    '/Applications/VLC.app/Contents/MacOS/VLC',
                    '--network-caching=10000',  # Increased network caching (10 seconds)
                    '--live-caching=10000',     # Cache for live streams
                    '--file-caching=10000',     # Cache for files
                    '--disc-caching=10000',     # Cache for discs
                    '--clock-jitter=0',         # Reduce clock jitter
                    '--clock-synchro=0',        # Clock synchronization
                    '--prefetch-buffer-size=1048576', # Prefetch buffer size (1MB)
                    selected_url
                ]
                
                print("Launching VLC...")
                subprocess.Popen(vlc_cmd)
                print("VLC launched successfully. Streaming video...")
                print("Press Ctrl+C to exit when done watching")
                
                # Keep the script running until user interrupts
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\nScript interrupted by user. Exiting...")
                
                return True
                
            except Exception as e:
                print(f"Error launching VLC: {str(e)}")
                return False
                
        finally:
            driver.quit()

    except Exception as e:
        print(f"Error streaming video: {str(e)}")
        if 'driver' in locals():
            driver.quit()
        return False

def main():
    parser = argparse.ArgumentParser(description='Stream video from vixsrc.to')
    parser.add_argument('video_id', help='Video ID from vixsrc.to')
    parser.add_argument('--tv', action='store_true', help='Treat as TV show')
    parser.add_argument('--movie', action='store_true', help='Treat as movie')
    parser.add_argument('--extract-only', action='store_true', help='Only extract the streaming URL, don\'t launch VLC')
    parser.add_argument('--season', type=int, help='Season number for TV shows')
    parser.add_argument('--episode', type=int, help='Episode number for TV shows')
    
    args = parser.parse_args()
    
    video_id = args.video_id
    is_tv = args.tv
    
    # If the video_id contains a URL, extract the ID
    if video_id.startswith('http'):
        # Extract the ID from the URL
        if 'vixsrc.to/tv/' in video_id:
            is_tv = True
            video_id = video_id.split('vixsrc.to/tv/')[1].rstrip('/')
        elif 'vixsrc.to/movie/' in video_id:
            is_tv = False
            video_id = video_id.split('vixsrc.to/movie/')[1].rstrip('/')
    
    # If the video_id contains slashes, it's likely a TV show with season/episode
    if '/' in video_id and not args.movie:
        is_tv = True
        print(f"Detected TV show format: {video_id}", file=sys.stderr)
    
    # Override with --movie flag if specified
    if args.movie:
        is_tv = False
    
    result = stream_video(video_id, is_tv, args.extract_only, args.season, args.episode)
    
    if args.extract_only:
        if result:
            # Ensure the URL has .m3u8 extension
            if not result.endswith('.m3u8'):
                # Simply add .m3u8 extension to the URL
                result = result + '.m3u8'
                print(f"Added .m3u8 extension to URL: {result}", file=sys.stderr)
            
            # Only write the URL to stdout for easy capture by other programs
            # Don't print with a label to avoid parsing issues
            sys.stdout.write(result)
            return 0
        else:
            print("Failed to extract URL!", file=sys.stderr)
            return 1
    else:
        if result:
            print("Streaming completed!")
            return 0
        else:
            print("Streaming failed!")
            return 1

if __name__ == "__main__":
    sys.exit(main())